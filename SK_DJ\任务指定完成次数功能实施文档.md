# 任务指定完成次数功能实施文档

## 📋 需求概述

### 功能描述
在现有任务系统基础上，增加任务可指定完成次数的功能。例如：一个任务可以设置为完成10次，玩家每次完成都能获得奖励，直到达到指定次数后任务结束。

### 核心需求
1. **任务配置**：支持设置任务最大完成次数
2. **进度跟踪**：记录任务已完成次数
3. **状态管理**：区分"可重复"和"已达上限"状态
4. **奖励发放**：每次完成都发放奖励
5. **UI显示**：显示完成进度（如：3/10次）

---

## 🏗️ 技术方案设计

### 1. 数据结构扩展

#### 1.1 TaskInfo 类扩展
```csharp
public class TaskInfo
{
    // 现有字段...
    public string 任务序号 { get; set; }
    public string 任务名 { get; set; }
    public List<task> 任务目标 { get; set; }
    public string 指定宠物 { get; set; }
    public string 任务奖励 { get; set; }
    public string 允许重复 { get; set; }           // 保持现有逻辑
    public string 已完成 { get; set; } = "0";
    public string 前置任务 { get; set; }
    public string 任务介绍 { get; set; }
    public bool? 网络任务 { get; set; }
    
    // 新增字段
    /// <summary>
    /// 最大完成次数，为空或"0"表示无限制（保持原有逻辑）
    /// </summary>
    public string 最大完成次数 { get; set; }
    
    /// <summary>
    /// 已完成次数，记录任务被完成的总次数
    /// </summary>
    public string 已完成次数 { get; set; } = "0";
}
```

#### 1.2 TaskPanel 类扩展
```csharp
public class TaskPanel
{
    // 现有字段...
    public string 任务介绍 { get; set; }
    public string 任务目标 { get; set; }
    public string 任务进度 { get; set; }
    public string 任务奖励 { get; set; }
    public string 任务名字 { get; set; }
    public string 是否完成 { get; set; }
    public bool 位置 { get; set; }
    public bool 一键完成 { get; set; }
    public string 循环任务 { get; set; }
    
    // 新增字段
    /// <summary>
    /// 完成次数信息，格式："3/10次"
    /// </summary>
    public string 完成次数信息 { get; set; }
    
    /// <summary>
    /// 是否已达最大完成次数
    /// </summary>
    public bool 已达上限 { get; set; }
}
```

### 2. 任务状态逻辑重新设计

#### 2.1 任务状态分类
```
1. 普通任务（最大完成次数为空或"0"）
   - 完成后标记为已完成，不可重复

2. 无限循环任务（允许重复="1"，最大完成次数为空或"0"）
   - 完成后可无限重复接取

3. 限次循环任务（允许重复="1"，最大完成次数>0）
   - 可重复完成，但有次数上限
   - 达到上限后不再可接取
```

#### 2.2 状态判断逻辑
```csharp
/// <summary>
/// 判断任务是否可以接取
/// </summary>
public bool CanReceiveTask(TaskInfo taskInfo)
{
    // 检查是否已接取
    if (IsTaskReceived(taskInfo.任务序号))
        return false;
    
    // 检查是否有最大完成次数限制
    if (!string.IsNullOrEmpty(taskInfo.最大完成次数) && 
        taskInfo.最大完成次数 != "0")
    {
        int maxCount = Convert.ToInt32(taskInfo.最大完成次数);
        int completedCount = Convert.ToInt32(taskInfo.已完成次数 ?? "0");
        
        // 已达最大完成次数
        if (completedCount >= maxCount)
            return false;
    }
    
    return true;
}

/// <summary>
/// 判断任务完成后的处理方式
/// </summary>
public TaskCompletionAction GetCompletionAction(TaskInfo taskInfo)
{
    // 非重复任务
    if (taskInfo.允许重复 != "1")
        return TaskCompletionAction.MarkCompleted;
    
    // 无限循环任务
    if (string.IsNullOrEmpty(taskInfo.最大完成次数) || 
        taskInfo.最大完成次数 == "0")
        return TaskCompletionAction.RemoveAndAllowReReceive;
    
    // 限次循环任务
    int maxCount = Convert.ToInt32(taskInfo.最大完成次数);
    int completedCount = Convert.ToInt32(taskInfo.已完成次数 ?? "0") + 1;
    
    if (completedCount >= maxCount)
        return TaskCompletionAction.MarkCompletedAndReachLimit;
    else
        return TaskCompletionAction.RemoveAndAllowReReceive;
}

public enum TaskCompletionAction
{
    MarkCompleted,                    // 标记为已完成
    RemoveAndAllowReReceive,         // 删除并允许重新接取
    MarkCompletedAndReachLimit       // 标记为已完成且达到上限
}
```

---

## 🔧 核心功能实现

### 1. 任务完成逻辑修改

#### 1.1 FulfilTask 方法重构
```csharp
internal bool FulfilTask(string 任务序号)
{
    var RESULT = GetTaskPanelInfo(任务序号, true);
    if (RESULT.是否完成 != "0") return false;
    
    var info = GetAppointedTask_HR(任务序号);
    if (info?.任务序号 == null) return false;
    
    // 现有验证逻辑...
    if (info.任务名.Contains("【活动】"))
    {
        if (BanTask == null || BanTask.Length == 0) return false;
    }
    if (BanTask != null && BanTask.Length != 0)
    {
        if (BanTask.Contains(info.任务序号)) return false;
    }
    
    // 发放奖励（现有逻辑保持不变）
    // ... 奖励发放代码 ...
    
    // 扣除材料（现有逻辑保持不变）
    // ... 材料扣除代码 ...
    
    // 新增：更新完成次数
    UpdateTaskCompletionCount(info);
    
    // 新增：根据完成次数决定后续处理
    var action = GetCompletionAction(info);
    HandleTaskCompletion(任务序号, info, action);
    
    return true;
}

/// <summary>
/// 更新任务完成次数
/// </summary>
private void UpdateTaskCompletionCount(TaskInfo taskInfo)
{
    // 更新任务定义中的完成次数
    var allTasks = GetAllTaskAim();
    var targetTask = allTasks.FirstOrDefault(t => t.任务序号 == taskInfo.任务序号);
    if (targetTask != null)
    {
        int currentCount = Convert.ToInt32(targetTask.已完成次数 ?? "0");
        targetTask.已完成次数 = (currentCount + 1).ToString();
        SaveTaskAim(allTasks);
    }
}

/// <summary>
/// 处理任务完成后的状态
/// </summary>
private void HandleTaskCompletion(string 任务序号, TaskInfo info, TaskCompletionAction action)
{
    info.已完成 = "0";
    info.任务目标 = new List<task>();
    info.任务奖励 = "";
    
    switch (action)
    {
        case TaskCompletionAction.MarkCompleted:
            // 普通任务：标记为已完成
            ChangeAppointedTask(任务序号, info);
            break;
            
        case TaskCompletionAction.RemoveAndAllowReReceive:
            // 循环任务：删除当前实例，允许重新接取
            AbortTask(任务序号);
            break;
            
        case TaskCompletionAction.MarkCompletedAndReachLimit:
            // 限次任务达到上限：标记为已完成
            ChangeAppointedTask(任务序号, info);
            break;
    }
}
```

### 2. 任务接取逻辑修改

#### 2.1 GetAllTask_AT 方法扩展
```csharp
internal List<TaskInfo> GetAllTask_AT() //取所有可领取任务
{
    List<TaskInfo> 任务1 = GetTasks_PHR();
    List<TaskInfo> 新列表 = new List<TaskInfo>();
    var 任务 = GetAllTaskAim();
    
    foreach (TaskInfo 信息 in 任务)
    {
        bool or = true;
        bool or1 = false;
        
        // 现有逻辑：检查是否已接取
        foreach (TaskInfo 玩家 in 任务1)
        {
            if (!string.IsNullOrEmpty(信息.前置任务) && 信息.前置任务 != "-1")
            {
                if (信息.前置任务 == 玩家.任务序号 && 玩家.已完成 == "0")
                {
                    or1 = true;
                }
            }

            if (信息.任务序号 != 玩家.任务序号) continue;
            or = false;
            break;
        }

        if (!string.IsNullOrEmpty(信息.前置任务) && 信息.前置任务 != "-1" && or)
        {
            or = or1;
        }

        if (TD) or = true;
        if (!or) continue;
        
        // 新增：检查完成次数限制
        if (!CanReceiveTask(信息))
            continue;
        
        if (!信息.任务名.Contains("联网"))
        {
            信息.任务目标 = null;
            信息.任务奖励 = "";
            信息.允许重复 = "";
        }

        新列表.Add(信息);
    }

    return 新列表;
}
```

### 3. 任务面板信息扩展

#### 3.1 GetTaskPanelInfo 方法扩展
```csharp
internal TaskPanel GetTaskPanelInfo(string 任务序号, bool 已接受)
{
    var 信息 = 已接受 ? GetAppointedTask_HR(任务序号) : GetAppointedTaskAim(任务序号);
    if (信息?.任务序号 == null)
    {
        return new TaskPanel() { 任务介绍 = "没有获取到任务（如果该任务为活动任务，请检查网络）" };
    }
    
    // 现有逻辑...
    TaskPanel 面板 = new TaskPanel() 
    { 
        位置 = 已接受, 
        任务名字 = 信息.任务名,
        循环任务 = 信息.允许重复 
    };
    
    // 新增：设置完成次数信息
    SetCompletionCountInfo(面板, 信息);
    
    // 现有逻辑：设置任务介绍、目标、进度等...
    // ... 其他代码保持不变 ...
    
    return 面板;
}

/// <summary>
/// 设置完成次数信息
/// </summary>
private void SetCompletionCountInfo(TaskPanel 面板, TaskInfo 信息)
{
    if (!string.IsNullOrEmpty(信息.最大完成次数) && 信息.最大完成次数 != "0")
    {
        int maxCount = Convert.ToInt32(信息.最大完成次数);
        int completedCount = Convert.ToInt32(信息.已完成次数 ?? "0");
        
        面板.完成次数信息 = $"{completedCount}/{maxCount}次";
        面板.已达上限 = completedCount >= maxCount;
        
        // 如果已达上限，在任务介绍中添加提示
        if (面板.已达上限)
        {
            面板.任务介绍 += "<br/><span style='color:orange'>该任务已达到最大完成次数限制</span>";
        }
    }
    else if (信息.允许重复 == "1")
    {
        // 无限循环任务
        int completedCount = Convert.ToInt32(信息.已完成次数 ?? "0");
        面板.完成次数信息 = $"{completedCount}/∞次";
        面板.已达上限 = false;
    }
    else
    {
        // 普通任务
        面板.完成次数信息 = "1/1次";
        面板.已达上限 = 信息.已完成 == "0";
    }
}
```

---

## 📊 配置示例

### 1. 任务配置示例

#### 1.1 限次循环任务
```json
{
    "任务序号": "20241203001",
    "任务名": "每日击杀训练",
    "任务目标": [
        {
            "Type": "击杀",
            "ID": "1001",
            "Num": "10",
            "inNum": "0"
        }
    ],
    "任务奖励": "金币,1000|经验,500",
    "允许重复": "1",
    "最大完成次数": "10",
    "已完成次数": "0",
    "任务介绍": "每日击杀训练，最多可完成10次"
}
```

#### 1.2 无限循环任务（保持现有逻辑）
```json
{
    "任务序号": "20241203002",
    "任务名": "无限刷怪任务",
    "任务目标": [
        {
            "Type": "击杀",
            "ID": "1002",
            "Num": "5",
            "inNum": "0"
        }
    ],
    "任务奖励": "金币,500",
    "允许重复": "1",
    "最大完成次数": "",
    "已完成次数": "0"
}
```

#### 1.3 普通任务（保持现有逻辑）
```json
{
    "任务序号": "20241203003",
    "任务名": "新手引导任务",
    "任务目标": [
        {
            "Type": "等级",
            "ID": "-1",
            "Num": "10",
            "inNum": "0"
        }
    ],
    "任务奖励": "道具,10001,1",
    "允许重复": "0",
    "最大完成次数": "",
    "已完成次数": "0"
}
```

---

## 🔄 兼容性保证

### 1. 向后兼容
- 新增字段使用默认值，不影响现有任务
- 现有任务逻辑完全保持不变
- 数据库结构向后兼容

### 2. 渐进式升级
```csharp
/// <summary>
/// 数据兼容性处理
/// </summary>
private void EnsureTaskCompatibility(TaskInfo task)
{
    // 确保新字段有默认值
    if (task.最大完成次数 == null)
        task.最大完成次数 = "";
        
    if (task.已完成次数 == null)
        task.已完成次数 = "0";
}
```

---

## 🎯 实施步骤

### 阶段一：数据结构扩展（1-2天）
1. 修改 TaskInfo 和 TaskPanel 类
2. 更新序列化/反序列化逻辑
3. 添加兼容性处理代码

### 阶段二：核心逻辑实现（2-3天）
1. 重构 FulfilTask 方法
2. 修改 GetAllTask_AT 方法
3. 扩展 GetTaskPanelInfo 方法
4. 添加完成次数管理逻辑

### 阶段三：UI界面适配（1-2天）
1. 修改任务面板显示
2. 添加完成次数显示
3. 更新任务列表界面

### 阶段四：测试验证（1-2天）
1. 单元测试
2. 集成测试
3. 兼容性测试

---

## ⚠️ 注意事项

### 1. 数据一致性
- 任务定义和玩家进度的完成次数需要同步
- 确保网络任务的完成次数正确处理

### 2. 性能考虑
- 避免频繁的文件IO操作
- 考虑使用缓存机制

### 3. 错误处理
- 处理数据格式异常
- 处理数值转换错误
- 提供友好的错误提示

---

## 💻 具体代码实现

### 1. TaskInfo.cs 修改

```csharp
using System.Collections.Generic;

namespace Shikong.Pokemon2.PCG
{
    public class TaskInfo
    {
        public string 任务序号 { get; set; }
        public string 任务名 { get; set; }
        public List<task> 任务目标 { get; set; }
        public string 指定宠物 { get; set; }
        public string 任务奖励 { get; set; }
        /// <summary>
        /// 为1就是允许重复,否则不允许重复
        /// </summary>
        public string 允许重复 { get; set; }
        /// <summary>
        /// 1为没有完成，0为已经完成
        /// </summary>
        public string 已完成 { get; set; } = "0";

        public string 前置任务 { get; set; }
        public string 任务介绍 { get; set; }
        public bool? 网络任务 { get; set; }

        // 新增字段
        /// <summary>
        /// 最大完成次数，为空或"0"表示无限制（保持原有逻辑）
        /// </summary>
        public string 最大完成次数 { get; set; }

        /// <summary>
        /// 已完成次数，记录任务被完成的总次数
        /// </summary>
        public string 已完成次数 { get; set; } = "0";
    }
}
```

### 2. TaskPanel.cs 修改

```csharp
namespace Shikong.Pokemon2.PCG
{
    public class TaskPanel
    {
        public string 任务介绍 { get; set; }
        public string 任务目标 { get; set; }
        public string 任务进度 { get; set; }
        public string 任务奖励 { get; set; }
        public string 任务名字 { get; set; }
        public string 是否完成 { get; set; }
        public bool 位置 { get; set; }
        public bool 一键完成 { get; set; }
        public string 循环任务 { get; set; }

        // 新增字段
        /// <summary>
        /// 完成次数信息，格式："3/10次"
        /// </summary>
        public string 完成次数信息 { get; set; }

        /// <summary>
        /// 是否已达最大完成次数
        /// </summary>
        public bool 已达上限 { get; set; }
    }
}
```

### 3. DataProcess.cs 核心方法修改

#### 3.1 添加枚举和辅助方法

```csharp
public enum TaskCompletionAction
{
    MarkCompleted,                    // 标记为已完成
    RemoveAndAllowReReceive,         // 删除并允许重新接取
    MarkCompletedAndReachLimit       // 标记为已完成且达到上限
}

/// <summary>
/// 判断任务是否可以接取
/// </summary>
private bool CanReceiveTask(TaskInfo taskInfo)
{
    // 检查是否有最大完成次数限制
    if (!string.IsNullOrEmpty(taskInfo.最大完成次数) &&
        taskInfo.最大完成次数 != "0")
    {
        int maxCount = Convert.ToInt32(taskInfo.最大完成次数);
        int completedCount = Convert.ToInt32(taskInfo.已完成次数 ?? "0");

        // 已达最大完成次数
        if (completedCount >= maxCount)
            return false;
    }

    return true;
}

/// <summary>
/// 判断任务完成后的处理方式
/// </summary>
private TaskCompletionAction GetCompletionAction(TaskInfo taskInfo)
{
    // 非重复任务
    if (taskInfo.允许重复 != "1")
        return TaskCompletionAction.MarkCompleted;

    // 无限循环任务
    if (string.IsNullOrEmpty(taskInfo.最大完成次数) ||
        taskInfo.最大完成次数 == "0")
        return TaskCompletionAction.RemoveAndAllowReReceive;

    // 限次循环任务
    int maxCount = Convert.ToInt32(taskInfo.最大完成次数);
    int completedCount = Convert.ToInt32(taskInfo.已完成次数 ?? "0") + 1;

    if (completedCount >= maxCount)
        return TaskCompletionAction.MarkCompletedAndReachLimit;
    else
        return TaskCompletionAction.RemoveAndAllowReReceive;
}

/// <summary>
/// 更新任务完成次数
/// </summary>
private void UpdateTaskCompletionCount(TaskInfo taskInfo)
{
    // 更新任务定义中的完成次数
    var allTasks = GetAllTaskAim();
    var targetTask = allTasks.FirstOrDefault(t => t.任务序号 == taskInfo.任务序号);
    if (targetTask != null)
    {
        int currentCount = Convert.ToInt32(targetTask.已完成次数 ?? "0");
        targetTask.已完成次数 = (currentCount + 1).ToString();
        SaveTaskAim(allTasks);
    }
}

/// <summary>
/// 处理任务完成后的状态
/// </summary>
private void HandleTaskCompletion(string 任务序号, TaskInfo info, TaskCompletionAction action)
{
    info.已完成 = "0";
    info.任务目标 = new List<task>();
    info.任务奖励 = "";

    switch (action)
    {
        case TaskCompletionAction.MarkCompleted:
            // 普通任务：标记为已完成
            ChangeAppointedTask(任务序号, info);
            break;

        case TaskCompletionAction.RemoveAndAllowReReceive:
            // 循环任务：删除当前实例，允许重新接取
            AbortTask(任务序号);
            break;

        case TaskCompletionAction.MarkCompletedAndReachLimit:
            // 限次任务达到上限：标记为已完成
            ChangeAppointedTask(任务序号, info);
            break;
    }
}

/// <summary>
/// 设置完成次数信息
/// </summary>
private void SetCompletionCountInfo(TaskPanel 面板, TaskInfo 信息)
{
    if (!string.IsNullOrEmpty(信息.最大完成次数) && 信息.最大完成次数 != "0")
    {
        int maxCount = Convert.ToInt32(信息.最大完成次数);
        int completedCount = Convert.ToInt32(信息.已完成次数 ?? "0");

        面板.完成次数信息 = $"{completedCount}/{maxCount}次";
        面板.已达上限 = completedCount >= maxCount;

        // 如果已达上限，在任务介绍中添加提示
        if (面板.已达上限)
        {
            面板.任务介绍 += "<br/><span style='color:orange'>该任务已达到最大完成次数限制</span>";
        }
    }
    else if (信息.允许重复 == "1")
    {
        // 无限循环任务
        int completedCount = Convert.ToInt32(信息.已完成次数 ?? "0");
        面板.完成次数信息 = $"{completedCount}/∞次";
        面板.已达上限 = false;
    }
    else
    {
        // 普通任务
        面板.完成次数信息 = "1/1次";
        面板.已达上限 = 信息.已完成 == "0";
    }
}

/// <summary>
/// 数据兼容性处理
/// </summary>
private void EnsureTaskCompatibility(TaskInfo task)
{
    // 确保新字段有默认值
    if (task.最大完成次数 == null)
        task.最大完成次数 = "";

    if (task.已完成次数 == null)
        task.已完成次数 = "0";
}
```

---

## 🧪 测试用例

### 1. 限次循环任务测试

```csharp
[TestCase]
public void TestLimitedRepeatableTask()
{
    // 创建限次循环任务（最多完成3次）
    var task = new TaskInfo
    {
        任务序号 = "TEST001",
        任务名 = "测试限次任务",
        允许重复 = "1",
        最大完成次数 = "3",
        已完成次数 = "0"
    };

    // 第1次完成
    Assert.IsTrue(CanReceiveTask(task));
    CompleteTask(task);
    Assert.AreEqual("1", task.已完成次数);

    // 第2次完成
    Assert.IsTrue(CanReceiveTask(task));
    CompleteTask(task);
    Assert.AreEqual("2", task.已完成次数);

    // 第3次完成
    Assert.IsTrue(CanReceiveTask(task));
    CompleteTask(task);
    Assert.AreEqual("3", task.已完成次数);

    // 第4次尝试接取（应该失败）
    Assert.IsFalse(CanReceiveTask(task));
}
```

### 2. 无限循环任务测试

```csharp
[TestCase]
public void TestInfiniteRepeatableTask()
{
    var task = new TaskInfo
    {
        任务序号 = "TEST002",
        任务名 = "测试无限任务",
        允许重复 = "1",
        最大完成次数 = "",
        已完成次数 = "0"
    };

    // 完成多次都应该可以接取
    for (int i = 1; i <= 10; i++)
    {
        Assert.IsTrue(CanReceiveTask(task));
        CompleteTask(task);
        Assert.AreEqual(i.ToString(), task.已完成次数);
    }
}
```

### 3. 普通任务测试

```csharp
[TestCase]
public void TestNormalTask()
{
    var task = new TaskInfo
    {
        任务序号 = "TEST003",
        任务名 = "测试普通任务",
        允许重复 = "0",
        最大完成次数 = "",
        已完成次数 = "0"
    };

    // 第1次可以接取
    Assert.IsTrue(CanReceiveTask(task));
    CompleteTask(task);

    // 完成后不能再接取
    task.已完成 = "0"; // 标记为已完成
    Assert.IsFalse(CanReceiveTask(task));
}
```

---

## 📋 数据库迁移脚本

### 1. 任务定义文件升级

```csharp
/// <summary>
/// 升级现有任务定义文件
/// </summary>
public void UpgradeTaskDefinitions()
{
    try
    {
        var tasks = GetAllTaskAim();
        bool needSave = false;

        foreach (var task in tasks)
        {
            if (task.最大完成次数 == null)
            {
                task.最大完成次数 = "";
                needSave = true;
            }

            if (task.已完成次数 == null)
            {
                task.已完成次数 = "0";
                needSave = true;
            }
        }

        if (needSave)
        {
            SaveTaskAim(tasks);
            Console.WriteLine("任务定义文件升级完成");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"任务定义文件升级失败: {ex.Message}");
    }
}
```

### 2. 玩家存档升级

```csharp
/// <summary>
/// 升级玩家任务存档
/// </summary>
public void UpgradePlayerTaskData()
{
    try
    {
        var playerTasks = GetTasks_PHR();
        bool needSave = false;

        foreach (var task in playerTasks)
        {
            EnsureTaskCompatibility(task);
            needSave = true;
        }

        if (needSave)
        {
            SaveTask_HR_List(playerTasks);
            Console.WriteLine("玩家任务存档升级完成");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"玩家任务存档升级失败: {ex.Message}");
    }
}
```

---

## 🎮 UI界面适配

### 1. 任务列表显示修改

```javascript
// 前端JavaScript代码示例
function displayTaskList(tasks) {
    tasks.forEach(task => {
        const taskElement = document.createElement('div');
        taskElement.className = 'task-item';

        let completionInfo = '';
        if (task.完成次数信息) {
            completionInfo = `<span class="completion-count">${task.完成次数信息}</span>`;
        }

        let statusClass = '';
        if (task.已达上限) {
            statusClass = 'task-limit-reached';
        } else if (task.循环任务 === '1') {
            statusClass = 'task-repeatable';
        }

        taskElement.innerHTML = `
            <div class="task-header ${statusClass}">
                <h3>${task.任务名字}</h3>
                ${completionInfo}
            </div>
            <div class="task-content">
                <p>${task.任务介绍}</p>
                <div class="task-progress">${task.任务进度}</div>
            </div>
        `;

        document.getElementById('task-list').appendChild(taskElement);
    });
}
```

### 2. CSS样式定义

```css
.task-item {
    border: 1px solid #ddd;
    margin: 10px 0;
    padding: 15px;
    border-radius: 5px;
}

.task-repeatable {
    border-left: 4px solid #4CAF50;
}

.task-limit-reached {
    border-left: 4px solid #FF9800;
    opacity: 0.7;
}

.completion-count {
    float: right;
    background: #2196F3;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.task-limit-reached .completion-count {
    background: #FF9800;
}
```

---

## 📈 性能优化建议

### 1. 缓存机制

```csharp
// 添加任务定义缓存
private static Dictionary<string, TaskInfo> _taskDefinitionCache = new Dictionary<string, TaskInfo>();
private static DateTime _cacheLastUpdate = DateTime.MinValue;

private List<TaskInfo> GetAllTaskAimCached()
{
    // 缓存5分钟
    if ((DateTime.Now - _cacheLastUpdate).TotalMinutes > 5)
    {
        var tasks = GetAllTaskAim();
        _taskDefinitionCache.Clear();
        foreach (var task in tasks)
        {
            _taskDefinitionCache[task.任务序号] = task;
        }
        _cacheLastUpdate = DateTime.Now;
    }

    return _taskDefinitionCache.Values.ToList();
}
```

### 2. 批量操作优化

```csharp
/// <summary>
/// 批量更新任务完成次数
/// </summary>
public void BatchUpdateTaskCompletionCount(List<string> taskIds)
{
    NoSave = true; // 启用批量模式

    try
    {
        foreach (string taskId in taskIds)
        {
            var task = GetAppointedTaskAim(taskId);
            if (task != null)
            {
                UpdateTaskCompletionCount(task);
            }
        }
    }
    finally
    {
        NoSave = false; // 恢复正常模式
        // 最后统一保存
        var allTasks = GetAllTaskAim();
        SaveTaskAim(allTasks);
    }
}
```

---

## 🔍 监控和日志

### 1. 任务完成统计

```csharp
/// <summary>
/// 记录任务完成统计
/// </summary>
private void LogTaskCompletion(TaskInfo task, int completionCount)
{
    string logMessage = $"任务完成: {task.任务名} (ID: {task.任务序号}) - 第{completionCount}次完成";

    if (!string.IsNullOrEmpty(task.最大完成次数) && task.最大完成次数 != "0")
    {
        int maxCount = Convert.ToInt32(task.最大完成次数);
        logMessage += $" (限制: {maxCount}次)";

        if (completionCount >= maxCount)
        {
            logMessage += " [已达上限]";
        }
    }

    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {logMessage}");

    // 可以添加到日志文件或数据库
    LogSystem.WriteLog("TaskCompletion", logMessage);
}
```

### 2. 异常处理和恢复

```csharp
/// <summary>
/// 任务数据一致性检查
/// </summary>
public void ValidateTaskDataConsistency()
{
    try
    {
        var allTasks = GetAllTaskAim();
        var playerTasks = GetTasks_PHR();

        foreach (var playerTask in playerTasks)
        {
            var definition = allTasks.FirstOrDefault(t => t.任务序号 == playerTask.任务序号);
            if (definition == null)
            {
                Console.WriteLine($"警告: 玩家任务 {playerTask.任务序号} 没有对应的定义");
                continue;
            }

            // 检查完成次数是否合理
            if (!string.IsNullOrEmpty(definition.最大完成次数) && definition.最大完成次数 != "0")
            {
                int maxCount = Convert.ToInt32(definition.最大完成次数);
                int completedCount = Convert.ToInt32(definition.已完成次数 ?? "0");

                if (completedCount > maxCount)
                {
                    Console.WriteLine($"错误: 任务 {definition.任务序号} 完成次数({completedCount})超过限制({maxCount})");
                    // 自动修复
                    definition.已完成次数 = maxCount.ToString();
                }
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"任务数据一致性检查失败: {ex.Message}");
    }
}
```

---

## 🚀 部署和发布

### 1. 发布前检查清单

- [ ] 数据结构修改完成
- [ ] 核心逻辑实现完成
- [ ] 兼容性处理完成
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] UI界面适配完成
- [ ] 性能测试通过
- [ ] 数据迁移脚本准备完成
- [ ] 回滚方案准备完成

### 2. 分阶段发布计划

#### 阶段1：内部测试版本
- 仅开放给内部测试人员
- 验证基本功能正常
- 收集初步反馈

#### 阶段2：小范围公测
- 开放给部分活跃用户
- 监控系统稳定性
- 收集用户体验反馈

#### 阶段3：全量发布
- 向所有用户开放
- 持续监控系统状态
- 及时处理问题反馈

### 3. 回滚方案

```csharp
/// <summary>
/// 紧急回滚方案：禁用新功能
/// </summary>
public class TaskFeatureToggle
{
    public static bool EnableLimitedRepeatTask = true;

    public static bool CanUseNewFeature()
    {
        return EnableLimitedRepeatTask;
    }

    public static void DisableNewFeature()
    {
        EnableLimitedRepeatTask = false;
        Console.WriteLine("任务限次功能已紧急关闭");
    }
}

// 在关键方法中添加开关检查
private bool CanReceiveTask(TaskInfo taskInfo)
{
    if (!TaskFeatureToggle.CanUseNewFeature())
    {
        // 回退到原有逻辑
        return true;
    }

    // 新功能逻辑...
}
```

---

## 📚 文档和培训

### 1. 用户手册更新

#### 1.1 任务系统说明
```markdown
# 任务系统使用指南

## 任务类型

### 普通任务
- 只能完成一次
- 完成后获得奖励，任务结束

### 循环任务
- 可以重复完成
- 每次完成都能获得奖励

### 限次循环任务（新增）
- 可以重复完成，但有次数限制
- 例如：每日任务最多完成10次
- 达到限制后不能再接取

## 任务界面说明

- 任务名称右侧显示完成次数（如：3/10次）
- 橙色边框表示已达完成次数上限
- 绿色边框表示可重复任务
```

#### 1.2 管理员配置指南
```markdown
# 任务配置指南

## 限次循环任务配置

1. 设置"允许重复"为"1"
2. 设置"最大完成次数"为具体数值（如"10"）
3. "已完成次数"会自动记录，初始为"0"

## 配置示例

```json
{
    "任务序号": "daily001",
    "任务名": "每日击杀任务",
    "允许重复": "1",
    "最大完成次数": "10",
    "已完成次数": "0"
}
```

## 注意事项

- 最大完成次数为空或"0"表示无限制
- 已完成次数会在任务定义中累计
- 达到上限的任务不会出现在可接取列表中
```

### 2. 开发者文档

#### 2.1 API接口说明
```csharp
/// <summary>
/// 任务管理API
/// </summary>
public class TaskManagerAPI
{
    /// <summary>
    /// 获取任务完成次数信息
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>格式："当前次数/最大次数"</returns>
    public string GetTaskCompletionInfo(string taskId);

    /// <summary>
    /// 检查任务是否可接取
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>true=可接取，false=不可接取</returns>
    public bool CanReceiveTask(string taskId);

    /// <summary>
    /// 重置任务完成次数（管理员功能）
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="resetCount">重置后的次数，默认为0</param>
    public void ResetTaskCompletionCount(string taskId, int resetCount = 0);
}
```

#### 2.2 事件通知机制
```csharp
/// <summary>
/// 任务事件参数
/// </summary>
public class TaskEventArgs : EventArgs
{
    public string TaskId { get; set; }
    public string TaskName { get; set; }
    public int CompletionCount { get; set; }
    public int MaxCompletionCount { get; set; }
    public bool ReachedLimit { get; set; }
}

/// <summary>
/// 任务事件管理器
/// </summary>
public static class TaskEventManager
{
    public static event EventHandler<TaskEventArgs> TaskCompleted;
    public static event EventHandler<TaskEventArgs> TaskLimitReached;

    public static void OnTaskCompleted(TaskEventArgs args)
    {
        TaskCompleted?.Invoke(null, args);

        if (args.ReachedLimit)
        {
            TaskLimitReached?.Invoke(null, args);
        }
    }
}
```

---

## 🔧 运维和监控

### 1. 关键指标监控

```csharp
/// <summary>
/// 任务系统监控指标
/// </summary>
public class TaskMetrics
{
    public static Dictionary<string, int> TaskCompletionStats = new Dictionary<string, int>();
    public static Dictionary<string, int> TaskLimitReachedStats = new Dictionary<string, int>();

    public static void RecordTaskCompletion(string taskId)
    {
        if (!TaskCompletionStats.ContainsKey(taskId))
            TaskCompletionStats[taskId] = 0;
        TaskCompletionStats[taskId]++;
    }

    public static void RecordTaskLimitReached(string taskId)
    {
        if (!TaskLimitReachedStats.ContainsKey(taskId))
            TaskLimitReachedStats[taskId] = 0;
        TaskLimitReachedStats[taskId]++;
    }

    public static string GenerateReport()
    {
        var report = new StringBuilder();
        report.AppendLine("=== 任务系统统计报告 ===");
        report.AppendLine($"报告时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine();

        report.AppendLine("任务完成统计:");
        foreach (var kvp in TaskCompletionStats.OrderByDescending(x => x.Value))
        {
            report.AppendLine($"  {kvp.Key}: {kvp.Value}次");
        }

        report.AppendLine();
        report.AppendLine("达到限制统计:");
        foreach (var kvp in TaskLimitReachedStats.OrderByDescending(x => x.Value))
        {
            report.AppendLine($"  {kvp.Key}: {kvp.Value}次");
        }

        return report.ToString();
    }
}
```

### 2. 自动化运维脚本

```csharp
/// <summary>
/// 任务系统维护工具
/// </summary>
public class TaskMaintenanceTools
{
    /// <summary>
    /// 每日重置限次任务（可用于每日任务）
    /// </summary>
    public static void DailyTaskReset()
    {
        try
        {
            var allTasks = new DataProcess().GetAllTaskAim();
            var dailyTasks = allTasks.Where(t => t.任务名.Contains("每日")).ToList();

            foreach (var task in dailyTasks)
            {
                if (!string.IsNullOrEmpty(task.最大完成次数) && task.最大完成次数 != "0")
                {
                    task.已完成次数 = "0";
                    Console.WriteLine($"重置每日任务: {task.任务名}");
                }
            }

            new DataProcess().SaveTaskAim(allTasks);
            Console.WriteLine($"每日任务重置完成，共重置 {dailyTasks.Count} 个任务");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"每日任务重置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 清理异常任务数据
    /// </summary>
    public static void CleanupAbnormalTaskData()
    {
        try
        {
            var allTasks = new DataProcess().GetAllTaskAim();
            int cleanupCount = 0;

            foreach (var task in allTasks)
            {
                // 修复负数完成次数
                if (!string.IsNullOrEmpty(task.已完成次数))
                {
                    if (int.TryParse(task.已完成次数, out int count) && count < 0)
                    {
                        task.已完成次数 = "0";
                        cleanupCount++;
                    }
                }

                // 修复超出限制的完成次数
                if (!string.IsNullOrEmpty(task.最大完成次数) && task.最大完成次数 != "0")
                {
                    int maxCount = Convert.ToInt32(task.最大完成次数);
                    int completedCount = Convert.ToInt32(task.已完成次数 ?? "0");

                    if (completedCount > maxCount)
                    {
                        task.已完成次数 = maxCount.ToString();
                        cleanupCount++;
                    }
                }
            }

            if (cleanupCount > 0)
            {
                new DataProcess().SaveTaskAim(allTasks);
                Console.WriteLine($"清理异常任务数据完成，修复 {cleanupCount} 个异常");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"清理异常任务数据失败: {ex.Message}");
        }
    }
}
```

---

## 📊 总结

### 功能特点
1. **向后兼容**：完全兼容现有任务系统
2. **灵活配置**：支持普通、无限循环、限次循环三种任务类型
3. **用户友好**：清晰的进度显示和状态提示
4. **性能优化**：缓存机制和批量操作支持
5. **监控完善**：详细的统计和日志记录

### 技术亮点
1. **渐进式升级**：新功能通过字段扩展实现，不破坏现有数据
2. **状态机设计**：清晰的任务状态转换逻辑
3. **异常处理**：完善的错误处理和数据恢复机制
4. **可维护性**：模块化设计，易于扩展和维护

### 预期效果
1. **提升用户体验**：更丰富的任务类型和清晰的进度显示
2. **增强游戏粘性**：限次任务机制鼓励玩家持续参与
3. **便于运营管理**：灵活的任务配置支持各种活动需求
4. **系统稳定性**：完善的监控和维护机制确保系统稳定运行

这份详细的实施文档为任务指定完成次数功能提供了完整的技术方案，确保新功能能够与现有系统完美集成，同时为后续的扩展和维护奠定了坚实的基础。
